"use client";

import { createTheme, ThemeProvider as MuiThemeProvider } from '@mui/material/styles';

const theme = createTheme({
  palette: {
    background: {
      main: "#F2E7E7",
      navbar: "#F5F5F5",
    },
    primary: {
      main: "#FF0000",
      red: "#FF0000",
      darkred: "#BB0000",
      black: "#0F0F0F",
      grey: "#999292",
      white: "#ffffff",
    },
    mode: "light",
    action: {
      active: "#efc8b1",
    }
  },
  typography: {
    body1: {
      fontWeight: 500,
      color: "#0F0F0F",
    },
    body2: {
      fontWeight: 400,
      color: "#0F0F0F",
    },
    h1: {
      fontWeight: 600,
      color: "#0F0F0F",
    },
    h2: {
      fontWeight: 400,
      color: "#BB0000",
    },
  },
  breakpoints: {
    values: {
      xs: 0,
      sm: 600,
      md: 900,
      lg: 1200,
      xl: 1536,
      mobile: 0,
      tablet: 640,
      laptop: 1024,
      desktop: 1200,
    },
  },
});

export default function ThemeProvider({ children }) {
  return (
    <MuiThemeProvider theme={theme}>
      {children}
    </MuiThemeProvider>
  );
} 