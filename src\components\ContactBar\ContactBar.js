"use client";

import * as React from 'react';
import { Stack } from "@mui/material";
import WhatsappIcon from "@mui/icons-material/WhatsApp";
import PhoneIcon from '@mui/icons-material/Phone';
import EmailIcon from "@mui/icons-material/Email";
import ContactItem from './ContactItem';

export default function ContactBar() {
  return (
    <Stack direction="row" spacing={2} justifyContent="center">
      <ContactItem 
        dest="https://wa.me/16473239480/" 
        icon={<WhatsappIcon className="inner" sx={{ color: '#383232', fontSize: 20 }} />}
      />
      <ContactItem 
        dest="tel:+1647-323-9480" 
        icon={<PhoneIcon className="inner" sx={{ color: '#383232', fontSize: 20 }} />}
      />
      <ContactItem 
        dest="mailto: <EMAIL>" 
        icon={<EmailIcon className="inner" sx={{ color: '#383232', fontSize: 20 }} />}
      />
    </Stack>
  );
} 