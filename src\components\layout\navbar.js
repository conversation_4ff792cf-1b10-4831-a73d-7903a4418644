"use client"

import * as React from 'react';
import { useState } from 'react';
import AppBar from '@mui/material/AppBar';
import Box from '@mui/material/Box';
import Toolbar from '@mui/material/Toolbar';
import Typography from '@mui/material/Typography';
import Container from '@mui/material/Container';
import Button from '@mui/material/Button';
import { useTranslations } from 'next-intl';
import { useRouter, usePathname } from 'next/navigation';
import { Link } from '@/i18n/navigation';
import Image from 'next/image';
import IconButton from '@mui/material/IconButton';
import MenuIcon from '@mui/icons-material/Menu';
import CloseIcon from '@mui/icons-material/Close';
import Drawer from '@mui/material/Drawer';
import Stack from '@mui/material/Stack';

function ResponsiveAppBar() {
  const [drawerOpen, setDrawerOpen] = useState(false);
  const t = useTranslations();
  const router = useRouter();
  const pathname = usePathname();

  const pages = [
    { name: t('NavBar.Home.title'), href: '/' },
    { name: t('NavBar.About.title'), href: '/about-us' },
    { name: t('NavBar.Solutions.title'), href: '/solutions' },
    { name: t('NavBar.Contact.title'), href: '/contact' }
  ];

  const handleLanguageSwitch = (locale) => {
    const currentPath = pathname;
    const newPath = currentPath.replace(/^\/[a-z]{2}/, `/${locale}`);
    router.push(newPath);
    setDrawerOpen(false);
  };

  return (
    <AppBar position="sticky" sx={{ bgcolor: "#383232", boxShadow: "none" }}>
      <Container maxWidth="xl">
        <Toolbar disableGutters sx={{ justifyContent: 'space-between', width: '100%' }}>
          <Link href="/" style={{ textDecoration: 'none', color: 'inherit' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
              <Image
                src="/hreflogo.png"
                width={40}
                height={40}
                alt="Href Tech"
              />
              <Typography
                variant="h6"
                sx={{
                  ml: 1,
                  fontFamily: 'sans-serif',
                  fontSize: '1.5rem',
                  fontWeight: 550,
                  color: 'white',
                  textDecoration: 'none',
                }}
              >
                href
              </Typography>
            </Box>
          </Link>

          {/* Desktop Nav */}
          <Box sx={{ flexGrow: 1, display: { xs: 'none', md: 'flex' }, justifyContent: 'center' }}>
            {pages.map((page) => (
              <Link key={page.name} href={page.href} style={{ textDecoration: 'none' }}>
                <Button
                  sx={{ my: 2, color: 'white', display: 'block' }}
                >
                  {page.name}
                </Button>
              </Link>
            ))}
          </Box>

          {/* Desktop Language Switch */}
          <Box sx={{ display: { xs: 'none', md: 'flex' } }}>
            <Button 
              onClick={() => handleLanguageSwitch('en')}
              sx={{ color: 'white', mx: 1 }}
            >
              {t('NavBar.language-en')}
            </Button>
            <Button 
              onClick={() => handleLanguageSwitch('zh')}
              sx={{ color: 'white', mx: 1 }}
            >
              {t('NavBar.language-zh')}
            </Button>
          </Box>

          {/* Mobile Menu Button (right) */}
          <Box sx={{ display: { xs: 'flex', md: 'none' }, ml: 'auto' }}>
            <IconButton
              size="large"
              aria-label="menu"
              onClick={() => setDrawerOpen(true)}
              color="inherit"
            >
              <MenuIcon />
            </IconButton>
          </Box>
        </Toolbar>
      </Container>
      {/* Mobile Drawer */}
      <Drawer
        anchor="right"
        open={drawerOpen}
        onClose={() => setDrawerOpen(false)}
        PaperProps={{ sx: { bgcolor: '#383232', color: 'white', width: 260, display: 'flex', flexDirection: 'column', justifyContent: 'space-between' } }}
      >
        <Box sx={{ p: 2, height: '100%', display: 'flex', flexDirection: 'column', flex: 1 }}>
          <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
            <IconButton onClick={() => setDrawerOpen(false)} sx={{ color: 'white' }}>
              <CloseIcon />
            </IconButton>
          </Box>
          <Stack spacing={2} sx={{ mt: 4 }}>
            {pages.map((page) => (
              <Link key={page.name} href={page.href} style={{ textDecoration: 'none', color: 'inherit' }}>
                <Button
                  fullWidth
                  sx={{ color: 'white', justifyContent: 'flex-start', fontSize: '1.1rem', textTransform: 'none' }}
                  onClick={() => setDrawerOpen(false)}
                >
                  {page.name}
                </Button>
              </Link>
            ))}
          </Stack>
          {/* Language Switch at Bottom */}
          <Box sx={{ mt: 'auto', pt: 2 }}>
            <Box sx={{ borderTop: '1px solid #444', pt: 2, display: 'flex', flexDirection: 'row', justifyContent: 'center', gap: 1 }}>
              <Button 
                onClick={() => handleLanguageSwitch('en')}
                sx={{ color: 'white', fontSize: '0.95rem', minWidth: 0, px: 1, textTransform: 'none' }}
              >
                {t('NavBar.language-en')}
              </Button>
              <Button 
                onClick={() => handleLanguageSwitch('zh')}
                sx={{ color: 'white', fontSize: '0.95rem', minWidth: 0, px: 1, textTransform: 'none' }}
              >
                {t('NavBar.language-zh')}
              </Button>
            </Box>
          </Box>
        </Box>
      </Drawer>
    </AppBar>
  );
}

export default ResponsiveAppBar;