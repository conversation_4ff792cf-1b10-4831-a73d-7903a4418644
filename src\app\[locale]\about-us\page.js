"use client";

import React from "react";
import { Box, Container, Typography, Grid, Card, CardMedia, CardContent, Stack, Divider, But<PERSON> } from "@mui/material";
import { motion } from "framer-motion";
import { useTranslations } from 'next-intl';




const Aboutus = () => {

  const t = useTranslations();

  // Import placeholder images - replace these with your actual team member photos
const teamMembers = [
  {
    name: t('AboutUs.Teammembers.name_1'),
    role: t('AboutUs.Teammembers.role_1'),
    image: "/Francis.jpg",
    bio: t('AboutUs.Teammembers.bio_1'),
    website: "https://francishui.com"
  },
  {
    name: t('AboutUs.Teammembers.name_2'),
    role: t('AboutUs.Teammembers.role_2'),
    image: "/Harry.jpg",
    bio: t('AboutUs.Teammembers.bio_2'),
    website: "https://tseharry.com/",
    reverse: true // Special flag for <PERSON> to override the default alternating pattern
  },
  {
    name: t('AboutUs.Teammembers.name_3'),
    role: t('AboutUs.Teammembers.role_3'),
    image: "/Eric.jpg",
    website: "https://humanhei.com/",
       bio: t('AboutUs.Teammembers.bio_3'),
  }
];

const products = [
  {
    title: t('AboutUs.products.title_1'),
    description: t('AboutUs.products.description_1'),
    image: "/WebsiteDesignDevelopment.jpg"
  },
  {
       title: t('AboutUs.products.title_2'),
    description: t('AboutUs.products.description_2'),
    image: "/network.jpg"
  }
];

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.5 }}
      style={{ backgroundColor: "black", color: "white" }}
    >
      {/* Banner Section */}
      <Box 
        sx={{ 
          height: { xs: "100vh", md: "95vh" },
          width: "100%",
          backgroundImage: `url('/aboutusbanner2.jpg')`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          mb: 6
        }}
      >
        <Typography 
          variant="h1" 
          component="h1" 
          sx={{ 
            color: "white", 
            textAlign: "center",
            fontSize: { xs: "2.5rem", md: "4rem" },
            textShadow: "2px 2px 4px rgba(0,0,0,0.5)"
          }}
        >
          {t("AboutUs.SubjectLine1")}
        </Typography>
      </Box>

      <Container maxWidth="lg" sx={{ color: "white" }}>
        {/* Team Members Section */}
        <Box sx={{ mb: 8 }}>
          <Typography 
            variant="h2" 
            component="h2" 
            sx={{ 
              mb: 4, 
              textAlign: "center",
              fontSize: { xs: "1.8rem", md: "2.5rem" },
              color: "white"
            }}
          >
            {t("AboutUs.Subject2Line1")}
          </Typography>

          <Box sx={{ width: '100%' }}>
            <Grid 
              container 
              spacing={4} 
              direction="row" 
              wrap={{ xs: 'wrap', md: 'nowrap' }}
              justifyContent="center" 
              alignItems="stretch"
            >
              {teamMembers.map((member, index) => (
                <Grid 
                  size={{ xs: 12, md: 4 }}
                  key={index}
                  sx={{ display: 'flex', height: '100%' }}
                >
                  <Card
                    elevation={4}
                    sx={{
                      bgcolor: "#222",
                      color: "white",
                      borderRadius: 4,
                      p: 3,
                      textAlign: 'center',
                      minHeight: 340,
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      justifyContent: 'flex-start',
                      alignItems: 'center',
                    }}
                  >
                    <Box
                      sx={{
                        display: 'flex',
                        justifyContent: 'center',
                        mb: 2,
                      }}
                    >
                      <Box
                        component="img"
                        src={member.image}
                        alt={member.name}
                        sx={{
                          width: 110,
                          height: 110,
                          borderRadius: '50%',
                          objectFit: 'cover',
                          boxShadow: 3,
                          border: '4px solid #383232',
                          background: '#111',
                        }}
                      />
                    </Box>
                    <Typography variant="h5" component="h3" sx={{ fontWeight: 600, mb: 1, color: 'white' }}>
                      {member.name}
                    </Typography>
                    <Typography variant="subtitle1" sx={{ color: 'primary.red', fontWeight: 500, mb: 1 }}>
                      {member.role}
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#ccc', fontSize: '1rem' }}>
                      {member.bio}
                    </Typography>
                    <Button 
                    variant="outlined" 
                    color="primary"
                    onClick={() => window.open(member.website, '_blank')}
                    sx={{ 
                      color: 'white', 
                      borderColor: 'primary.main',
                      '&:hover': {
                        borderColor: 'primary.light',
                        backgroundColor: 'rgba(255, 255, 255, 0.1)'
                      }
                    }}
                  >
                    {t('General.LearnMore')}
                  </Button>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Box>
          
        </Box>

        <Divider sx={{ mb: 8, bgcolor: "gray" }} />

        {/* Products Section */}
        <Box sx={{ mb: 8 }}>
          <Typography 
            variant="h2" 
            component="h2" 
            sx={{ 
              mb: 4, 
              textAlign: "center",
              fontSize: { xs: "1.8rem", md: "2.5rem" },
              color: "white"
            }}
          >
           {t("AboutUs.Subject3Line1")}
          </Typography>

          <Grid container spacing={4}>
            {products.map((product, index) => (
              <Grid size={{ xs: 12, md: 6 }} key={index}>
                <Card elevation={3} sx={{ bgcolor: "#222" }}>
                  <CardMedia
                    component="img"
                    image={product.image}
                    alt={product.title}
                    sx={{ height: 250 }}
                  />
                  <CardContent sx={{ color: "white" }}>
                    <Typography variant="h3" component="h3" sx={{ mb: 2, fontSize: { xs: "1.5rem", md: "1.8rem" }, color: "white" }}>
                      {product.title}
                    </Typography>
                    <Typography variant="body1" sx={{ color: "white" }}>
                      {product.description}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>
      </Container>
    </motion.div>
  );
}

export default Aboutus;