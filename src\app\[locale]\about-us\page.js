"use client";

import React from "react";
import { Box, Container, Typography, Grid, Card, CardMedia, CardContent, Stack, Divider, <PERSON><PERSON> } from "@mui/material";
import { motion } from "framer-motion";

// Import placeholder images - replace these with your actual team member photos
const teamMembers = [
  {
    name: "<PERSON>",
    role: "CEO & Founder",
    image: "/Francis.jpg",
    bio: "<PERSON> is a seasoned software developer with 5 years of experience specializing in full-stack development, system architecture, and DevOps. He has a strong background in the banking and financial services industry, having worked under Hong Kong Monetary Authority standards. <PERSON> has led projects involving microservices deployment, performance optimization, and cloud integration, with expertise in Java and React. As the Founder, he brings a blend of technical excellence and strategic leadership to drive the company's vision.",
    website: "https://francishui.com"
  },
  {
    name: "<PERSON>",
    role: "Technical Director",
    image: "/Harry.jpg",
    bio: "<PERSON> oversees all technical aspects of our projects.",
    website: "https://tseharry.com/",
    reverse: true // Special flag for <PERSON> to override the default alternating pattern
  },
  {
    name: "<PERSON>",
    role: "Lead Designer",
    image: "/Eric.jpg",
    website: "https://humanhei.com/",
    bio: "<PERSON> brings creative vision and user experience expertise to every project."
  }
];

const products = [
  {
    title: "Website Design & Development",
    description: "We create custom websites that are responsive, fast, and optimized for search engines. Our development process focuses on creating seamless user experiences while ensuring your site stands out from the competition.",
    image: "/WebsiteDesignDevelopment.jpg"
  },
  {
    title: "Custom Network Solutions",
    description: "Our team designs and implements tailored network infrastructure to meet your specific business needs. From small office setups to enterprise-level solutions, we ensure secure, efficient, and scalable networks.",
    image: "/network.jpg"
  }
];

const Aboutus = () => {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.5 }}
      style={{ backgroundColor: "black", color: "white" }}
    >
      {/* Banner Section */}
      <Box 
        sx={{ 
          height: { xs: "100vh", md: "95vh" },
          width: "100%",
          backgroundImage: `url('/aboutusbanner2.jpg')`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          mb: 6
        }}
      >
        <Typography 
          variant="h1" 
          component="h1" 
          sx={{ 
            color: "white", 
            textAlign: "center",
            fontSize: { xs: "2.5rem", md: "4rem" },
            textShadow: "2px 2px 4px rgba(0,0,0,0.5)"
          }}
        >
          About Our Team
        </Typography>
      </Box>

      <Container maxWidth="lg" sx={{ color: "white" }}>
        {/* Team Members Section */}
        <Box sx={{ mb: 8 }}>
          <Typography 
            variant="h2" 
            component="h2" 
            sx={{ 
              mb: 4, 
              textAlign: "center",
              fontSize: { xs: "1.8rem", md: "2.5rem" },
              color: "white"
            }}
          >
            Meet Our Team Members
          </Typography>

          <Box sx={{ width: '100%' }}>
            <Grid 
              container 
              spacing={4} 
              direction="row" 
              wrap={{ xs: 'wrap', md: 'nowrap' }}
              justifyContent="center" 
              alignItems="stretch"
            >
              {teamMembers.map((member, index) => (
                <Grid 
                  size={{ xs: 12, md: 4 }}
                  key={index}
                  sx={{ display: 'flex', height: '100%' }}
                >
                  <Card
                    elevation={4}
                    sx={{
                      bgcolor: "#222",
                      color: "white",
                      borderRadius: 4,
                      p: 3,
                      textAlign: 'center',
                      minHeight: 340,
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      justifyContent: 'flex-start',
                      alignItems: 'center',
                    }}
                  >
                    <Box
                      sx={{
                        display: 'flex',
                        justifyContent: 'center',
                        mb: 2,
                      }}
                    >
                      <Box
                        component="img"
                        src={member.image}
                        alt={member.name}
                        sx={{
                          width: 110,
                          height: 110,
                          borderRadius: '50%',
                          objectFit: 'cover',
                          boxShadow: 3,
                          border: '4px solid #383232',
                          background: '#111',
                        }}
                      />
                    </Box>
                    <Typography variant="h5" component="h3" sx={{ fontWeight: 600, mb: 1, color: 'white' }}>
                      {member.name}
                    </Typography>
                    <Typography variant="subtitle1" sx={{ color: 'primary.red', fontWeight: 500, mb: 1 }}>
                      {member.role}
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#ccc', fontSize: '1rem' }}>
                      {member.bio}
                    </Typography>
                    <Button 
                    variant="outlined" 
                    color="primary"
                    onClick={() => window.open(member.website, '_blank')}
                    sx={{ 
                      color: 'white', 
                      borderColor: 'primary.main',
                      '&:hover': {
                        borderColor: 'primary.light',
                        backgroundColor: 'rgba(255, 255, 255, 0.1)'
                      }
                    }}
                  >
                    Learn More
                  </Button>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Box>
          
        </Box>

        <Divider sx={{ mb: 8, bgcolor: "gray" }} />

        {/* Products Section */}
        <Box sx={{ mb: 8 }}>
          <Typography 
            variant="h2" 
            component="h2" 
            sx={{ 
              mb: 4, 
              textAlign: "center",
              fontSize: { xs: "1.8rem", md: "2.5rem" },
              color: "white"
            }}
          >
            Our Services
          </Typography>

          <Grid container spacing={4}>
            {products.map((product, index) => (
              <Grid size={{ xs: 12, md: 6 }} key={index}>
                <Card elevation={3} sx={{ bgcolor: "#222" }}>
                  <CardMedia
                    component="img"
                    image={product.image}
                    alt={product.title}
                    sx={{ height: 250 }}
                  />
                  <CardContent sx={{ color: "white" }}>
                    <Typography variant="h3" component="h3" sx={{ mb: 2, fontSize: { xs: "1.5rem", md: "1.8rem" }, color: "white" }}>
                      {product.title}
                    </Typography>
                    <Typography variant="body1" sx={{ color: "white" }}>
                      {product.description}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>
      </Container>
    </motion.div>
  );
}

export default Aboutus;