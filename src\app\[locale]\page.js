"use client";

import React from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON>graphy, Fade, Stack, Box, Button, Card, CardContent, CardMedia, Container } from "@mui/material";
import {useTranslations} from 'next-intl';
import { motion } from "framer-motion";


export default function Home() 
{
  const t = useTranslations();
  
  const services = [
    {
      title:  t('HomePage.Service.Title_1'),
      description: t('HomePage.Service.description_1'),
      image: "/WebsiteDesignDevelopment.jpg"
    },
    {
       title:  t('HomePage.Service.Title_2'),
      description: t('HomePage.Service.description_2'),
      image: "/network.jpg"
    },
    {
         title:  t('HomePage.Service.Title_3'),
      description: t('HomePage.Service.description_3'),
      image: "/WebsiteDesignDevelopment.jpg"
    },
    {
      title:  t('HomePage.Service.Title_4'),
      description: t('HomePage.Service.description_4'),
      image: "/network.jpg"
    }
  ];

  const clients = [
    {
      name: t('HomePage.Clients.name_1'),
      description: t('HomePage.Clients.name_1'),
      website: "https://starlightopera.com",
      // 這裡預留位置放團體icon - 您需要上傳圖標並更新路徑
      icon: "/starlightopera.png"
    }
  ];

  

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.5 }}
      style={{ backgroundColor: "black", color: "white" }}
    >
      {/* Hero Section */}
      <Box 
        sx={{ 
          height: { xs: "100vh", md: "95vh" },
          width: "100%",
          backgroundImage: `url('/aboutusbanner2.jpg')`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          position: "relative"
        }}
      >
        <Box 
          sx={{
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: "rgba(0,0,0,0.5)",
            zIndex: 1
          }}
        />
        <Stack 
          direction="column" 
          spacing={4} 
          sx={{ 
            zIndex: 2, 
            textAlign: "center", 
            maxWidth: "800px", 
            px: 3 
          }}
        >
          <Typography 
            variant="h1" 
            component="h1" 
            sx={{ 
              color: "white", 
              textAlign: "center",
              fontSize: { xs: "2.5rem", md: "4rem" },
              textShadow: "2px 2px 4px rgba(0,0,0,0.5)",
              fontWeight: "bold"
            }}
          >
           {t('HomePage.SubjectLine1')}
          </Typography>
          <Typography 
            variant="h5" 
            sx={{ 
              color: "white", 
              textAlign: "center",
              fontSize: { xs: "1.2rem", md: "1.5rem" },
              textShadow: "1px 1px 2px rgba(0,0,0,0.5)"
            }}
          >
            {t('HomePage.SubjectLine2')}
          </Typography>
          <Stack 
            direction={{ xs: "column", sm: "row" }} 
            spacing={2} 
            justifyContent="center"
          >
            <Button 
              variant="contained" 
              size="large"
              sx={{ 
                bgcolor: "primary.red", 
                color: "white", 
                px: 4, 
                py: 1.5,
                "&:hover": { bgcolor: "primary.darkred" }
              }}
            >
              {t('General.Ourservices')}
            </Button>
            <Button 
              variant="outlined" 
              size="large"
              sx={{ 
                borderColor: "white", 
                color: "white", 
                px: 4, 
                py: 1.5,
                "&:hover": { borderColor: "primary.red", color: "primary.red" }
              }}
            >
              {t('General.ContactUs')}
            </Button>
          </Stack>
        </Stack>
      </Box>

      {/* About Section */}
      <Box sx={{ py: 10, px: 3, bgcolor: "black" }}>
        <Container maxWidth="lg">
          <Grid container spacing={6} alignItems="center">
            <Grid size={{ xs: 12, md: 6 }}>
              <Typography variant="h2" component="h2" sx={{ mb: 4, color: "white", fontSize: { xs: "2rem", md: "2.5rem" } }}>
                {t('HomePage.Intro.Topic')}
              </Typography>
              <Typography variant="body1" sx={{ mb: 4, color: "white", fontSize: { xs: "1rem", md: "1.1rem" } }}>
                {t("HomePage.Intro.IntroText")}
              </Typography>
              <Typography variant="body1" sx={{ mb: 4, color: "white", fontSize: { xs: "1rem", md: "1.1rem" } }}>
                {t("HomePage.Intro.Content")}
              </Typography>
              <Button 
                variant="contained" 
                sx={{ 
                  bgcolor: "primary.red", 
                  color: "white", 
                  px: 4, 
                  py: 1.5,
                  "&:hover": { bgcolor: "primary.darkred" }
                }}
              >
                 {t("General.LearnMore")}
              </Button>
            </Grid>
            <Grid size={{ xs: 12, md: 6 }}>
              <Box 
                component="img"
                src="/WebsiteDesignDevelopment.jpg"
                alt="About Href Technology"
                sx={{ 
                  width: "100%", 
                  borderRadius: 2,
                  boxShadow: "0px 4px 20px rgba(0,0,0,0.1)"
                }}
              />
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Services Section */}
      <Box sx={{ py: 10, px: 3, bgcolor: "#111" }}>
        <Container maxWidth="lg">
          <Typography 
            variant="h2" 
            component="h2" 
            sx={{ 
              mb: 6, 
              textAlign: "center",
              fontSize: { xs: "2rem", md: "2.5rem" },
              color: "white"
            }}
          >
            {t("HomePage.OurServices")}
          </Typography>

          <Grid container spacing={4}>
            {services.map((service, index) => (
              <Grid size={{ xs: 12, sm: 6, md: 3 }} key={index}>
                <Card sx={{ height: "100%", bgcolor: "#222", color: "white" }}>
                  <CardMedia
                    component="img"
                    height="140"
                    image={service.image}
                    alt={service.title}
                  />
                  <CardContent>
                    <Typography variant="h5" component="h3" sx={{ mb: 2, color: "white" }}>
                      {service.title}
                    </Typography>
                    <Typography variant="body2" sx={{ color: "#ccc" }}>
                      {service.description}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Our Clients Section */}
      <Box sx={{ py: 10, px: 3, bgcolor: "black" }}>
        <Container maxWidth="lg">
          <Typography 
            variant="h2" 
            component="h2" 
            sx={{ 
              mb: 6, 
              textAlign: "center",
              fontSize: { xs: "2rem", md: "2.5rem" },
              color: "white"
            }}
          >
             {t("HomePage.OurClients")}
          </Typography>

          <Grid container spacing={4} justifyContent="center">
            {clients.map((client, index) => (
              <Grid size={{ xs: 12, md: 8 }} key={index}>
                <Card sx={{ bgcolor: "#222", color: "white", p: 4 }}>
                  <Grid container spacing={4} alignItems="center">
                    <Grid size={{ xs: 12, md: 3 }} sx={{ display: "flex", justifyContent: "center" }}>
                      <Box
                        component="img"
                        src={client.icon}
                        alt={client.name}
                        sx={{ 
                          width: "150px", 
                          height: "150px", 
                          borderRadius: "50%",
                          objectFit: "cover",
                          backgroundColor: "white",
                          border: "3px solid #fff"
                        }}
                      />
                    </Grid>
                    <Grid size={{ xs: 12, md: 9 }}>
                      <Typography variant="h4" component="h3" sx={{ mb: 2, color: "white" }}>
                        {client.name}
                      </Typography>
                      <Typography variant="body1" sx={{ mb: 3, color: "#ccc" }}>
                        {client.description}
                      </Typography>
                      <Button 
                        variant="outlined" 
                        onClick={() => window.open(client.website, '_blank')}
                        sx={{ 
                          color: "white", 
                          borderColor: "primary.red",
                          "&:hover": { 
                            borderColor: "primary.light",
                            backgroundColor: "rgba(255, 0, 0, 0.1)" 
                          }
                        }}
                      >
                        {t("General.VisitWebsite")}
                      </Button>
                    </Grid>
                  </Grid>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* CTA Section */}
      <Box 
        sx={{ 
          py: 10, 
          px: 3, 
          bgcolor: "black",
          backgroundImage: `linear-gradient(rgba(0,0,0,0.8), rgba(0,0,0,0.8)), url("/network.jpg")`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          textAlign: "center"
        }}
      >
        <Container maxWidth="md">
          <Typography 
            variant="h2" 
            component="h2" 
            sx={{ 
              mb: 4, 
              color: "white",
              fontSize: { xs: "2rem", md: "2.5rem" }
            }}
          >
            {t("HomePage.Footer.Topic")}
          </Typography>
          <Typography 
            variant="body1" 
            sx={{ 
              mb: 6, 
              color: "white",
              fontSize: { xs: "1rem", md: "1.2rem" }
            }}
          >
            {t("HomePage.Footer.Content")}
          </Typography>
          <Button 
            variant="contained" 
            size="large"
            sx={{ 
              bgcolor: "primary.red", 
              color: "white", 
              px: 6, 
              py: 2,
              fontSize: "1.1rem",
              "&:hover": { bgcolor: "primary.darkred" }
            }}
          >
            {t("General.GetStart")}
          </Button>
        </Container>
      </Box>
    </motion.div>
  )
}