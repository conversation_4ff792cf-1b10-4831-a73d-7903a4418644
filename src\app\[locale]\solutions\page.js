"use client";

import React from 'react';
import { <PERSON>rid, <PERSON><PERSON><PERSON>, <PERSON>ack, Box, Button, Card, CardContent, CardMedia, Container } from "@mui/material";
import { motion } from "framer-motion";
import { useTranslations } from 'next-intl';

function Solutions() {

    const t = useTranslations();
    
  const solutions = [
    {
      title: t('Solutions.WebsiteTitle'),
      description: t('Solutions.WebsiteDescription'),
      image: "/WebsiteDesignDevelopment.jpg",
      features: [
        t('Solutions.Websitefeatures.1'),
        t('Solutions.Websitefeatures.2'),
        t('Solutions.Websitefeatures.3'),
        t('Solutions.Websitefeatures.4'),
        t('Solutions.Websitefeatures.5')
      ]
    },
    {
      title: t('Solutions.NetworkTitle'),
      description: t('Solutions.NetworkDescription'),
      features: [
        t('Solutions.Networkfeatures.1'),
        t('Solutions.Networkfeatures.2'),
        t('Solutions.Networkfeatures.3'),
        t('Solutions.Networkfeatures.4'),
        t('Solutions.Networkfeatures.5')
      ]
    },
    {
      title:  t('Solutions.CloudTitle'),
      description: t('Solutions.CloudDescription'),
      image: "/WebsiteDesignDevelopment.jpg",
      features: [
        t('Solutions.Cloudfeatures.1'),
        t('Solutions.Cloudfeatures.2'),
        t('Solutions.Cloudfeatures.3'),
        t('Solutions.Cloudfeatures.4'),
        t('Solutions.Cloudfeatures.5')
      ]
    },
    {
      title: t('Solutions.ConsultingTitle'),
      description: t('Solutions.ConsultingDescription'),
      image: "/network.jpg",
      features: [
        t('Solutions.Consultingfeatures.1'),
        t('Solutions.Consultingfeatures.2'),
        t('Solutions.Consultingfeatures.3'),
        t('Solutions.Consultingfeatures.4'),
        t('Solutions.Consultingfeatures.5')
      ]
    }
  ];

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.5 }}
      style={{ backgroundColor: "black", color: "white" }}
    >
      {/* Hero Section */}
      <Box 
        sx={{ 
          height: { xs: "60vh", md: "70vh" },
          width: "100%",
          backgroundImage: `linear-gradient(rgba(0,0,0,0.7), rgba(0,0,0,0.7)), url('/aboutusbanner2.jpg')`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          position: "relative"
        }}
      >
        <Stack 
          direction="column" 
          spacing={4} 
          sx={{ 
            zIndex: 2, 
            textAlign: "center", 
            maxWidth: "800px", 
            px: 3 
          }}
        >
          <Typography 
            variant="h1" 
            component="h1" 
            sx={{ 
              color: "white", 
              textAlign: "center",
              fontSize: { xs: "2.5rem", md: "4rem" },
              textShadow: "2px 2px 4px rgba(0,0,0,0.5)",
              fontWeight: "bold"
            }}
          >
           {t("Solutions.SubjectLine1")}
          </Typography>
          <Typography 
            variant="h5" 
            sx={{ 
              color: "white", 
              textAlign: "center",
              fontSize: { xs: "1.2rem", md: "1.5rem" },
              textShadow: "1px 1px 2px rgba(0,0,0,0.5)"
            }}
          >
            {t("Solutions.Title")}
          </Typography>
        </Stack>
      </Box>

      {/* Solutions Section */}
      <Container maxWidth="lg" sx={{ py: 8 }}>
        <Typography 
          variant="h2" 
          component="h2" 
          sx={{ 
            mb: 6, 
            textAlign: "center",
            fontSize: { xs: "2rem", md: "2.5rem" },
            color: "white"
          }}
        >
          {t("Solutions.Subject2Line1")}
        </Typography>
        
        <Typography 
          variant="body1" 
          sx={{ 
            mb: 8, 
            textAlign: "center",
            fontSize: { xs: "1rem", md: "1.1rem" },
            color: "#ccc",
            maxWidth: "800px",
            mx: "auto"
          }}
        >
          {t("Solutions.Subject2Content")}
        </Typography>

        {solutions.map((solution, index) => (
          <Box 
            key={index} 
            sx={{ 
              mb: 12,
              pb: 6,
              borderBottom: index < solutions.length - 1 ? "1px solid #333" : "none"
            }}
          >
            <Grid container spacing={6} direction={index % 2 === 0 ? "row" : "row-reverse"}>
              <Grid item xs={12} md={6}>
                <Box 
                  component="img"
                  src={solution.image}
                  alt={solution.title}
                  sx={{ 
                    width: "100%", 
                    borderRadius: 2,
                    boxShadow: "0px 4px 20px rgba(0,0,0,0.3)"
                  }}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography 
                  variant="h3" 
                  component="h3" 
                  sx={{ 
                    mb: 3,
                    color: "white",
                    fontSize: { xs: "1.8rem", md: "2.2rem" }
                  }}
                >
                  {solution.title}
                </Typography>
                <Typography 
                  variant="body1" 
                  sx={{ 
                    mb: 4,
                    color: "#ccc",
                    fontSize: { xs: "1rem", md: "1.1rem" }
                  }}
                >
                  {solution.description}
                </Typography>
                <Typography 
                  variant="h6" 
                  component="h4" 
                  sx={{ 
                    mb: 2,
                    color: "primary.red",
                    fontSize: { xs: "1.1rem", md: "1.3rem" }
                  }}
                >
                  Key Features:
                </Typography>
                <Stack spacing={1}>
                  {solution.features.map((feature, featureIndex) => (
                    <Typography 
                      key={featureIndex}
                      variant="body2" 
                      sx={{ 
                        color: "#ccc",
                        fontSize: { xs: "0.9rem", md: "1rem" },
                        display: "flex",
                        alignItems: "center"
                      }}
                    >
                      • {feature}
                    </Typography>
                  ))}
                </Stack>
                <Button 
                  variant="contained" 
                  sx={{ 
                    mt: 3,
                    bgcolor: "primary.red", 
                    color: "white", 
                    px: 4, 
                    py: 1.5,
                    "&:hover": { bgcolor: "primary.darkred" }
                  }}
                >
                    {t("General.LearnMore")}
                </Button>
              </Grid>
            </Grid>
          </Box>
        ))}
      </Container>
    </motion.div>
  );
}

export default Solutions; 