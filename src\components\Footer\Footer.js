"use client";

import React from 'react';
import { Box, Container, Typography, Stack } from "@mui/material";
import { useTranslations } from 'next-intl';
import EmailOutlinedIcon from '@mui/icons-material/EmailOutlined';
import LocalPhoneOutlinedIcon from '@mui/icons-material/LocalPhoneOutlined';
import ContactBar from '../ContactBar/ContactBar';

export default function Footer() {
  const t = useTranslations();
  
  return (
    <Box 
      sx={{ 
        bgcolor: '#383232',
        py: 4,
        borderTop: '1px solid #333'
      }}
    >
      <Container maxWidth="lg">
        <Stack spacing={3}>
          {/* Contact Us Title */}
          <Typography 
            variant="h4" 
            component="h2" 
            sx={{ 
              textAlign: 'center',
              color: 'white',
              fontWeight: 'bold'
            }}
          >
            {t("Footer.ContactUs")}
          </Typography>

          {/* Contact Information */}
          <Stack 
            direction={{ xs: 'column', md: 'row' }} 
            spacing={3} 
            justifyContent="center" 
            alignItems="center"
          >
            {/* Phone */}
            <Stack direction="row" spacing={1} alignItems="center">
              <LocalPhoneOutlinedIcon sx={{ color: "primary.main", fontSize: 24 }} />
              <Typography variant="body1" sx={{ color: 'white' }}>
                +****************
              </Typography>
            </Stack>

            {/* Email */}
            <Stack direction="row" spacing={1} alignItems="center">
              <EmailOutlinedIcon sx={{ color: "primary.main", fontSize: 24 }} />
              <Typography variant="body1" sx={{ color: 'white' }}>
                <EMAIL>
              </Typography>
            </Stack>
          </Stack>

          {/* Social Icons */}
          <Box sx={{ textAlign: 'center' }}>
            <ContactBar />
          </Box>

          {/* Copyright */}
          <Typography 
            variant="body2" 
            sx={{ 
              textAlign: 'center',
              color: '#ccc',
              pt: 2,
              borderTop: '1px solid #333'
            }}
          >
            {t("Footer.Copyright-prefix")}{new Date().getFullYear()}{t("Footer.Copyright-suffix")}
          </Typography>
        </Stack>
      </Container>
    </Box>
  );
} 