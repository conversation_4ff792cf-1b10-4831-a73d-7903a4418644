"use client";

import * as React from 'react';
import { <PERSON><PERSON>, Link } from "@mui/material";
import { makeStyles } from '@mui/styles';

const useStyles = makeStyles(({
  outer: {
    transition: "0.4s",
    backgroundColor: "#ffffff !important",
    '&:hover': {
      background: "#FF0000 !important", 
      transform: "translate(0, -8px)",
      "& .inner": {
        color: "#FFFFFF !important"
      }
    },
  },
}));

export default function ContactItem(props) {
  const classes = useStyles();

  return (
    <Link href={props.dest} target="_blank" sx={{outline: 'none'}}>
      <Avatar 
        className={classes.outer} 
        sx={{ 
          height: 40, 
          width: 40, 
          position: "static", 
          bgcolor: "#ffffff"
        }}
      >
        {props.icon}
      </Avatar>
    </Link>
  );
} 