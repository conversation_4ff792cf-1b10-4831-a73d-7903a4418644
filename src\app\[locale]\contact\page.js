"use client";

import React, { useState } from 'react';
import { <PERSON>, Fade, Stack, Grid, <PERSON><PERSON>graphy, TextField, Button, Snackbar, Alert } from "@mui/material";
import { useTranslations } from 'next-intl';
import LocationOnOutlinedIcon from '@mui/icons-material/LocationOnOutlined';
import EmailOutlinedIcon from '@mui/icons-material/EmailOutlined';
import LocalPhoneOutlinedIcon from '@mui/icons-material/LocalPhoneOutlined';
import SendIcon from '@mui/icons-material/Send';
import emailjs from '@emailjs/browser';


const SERVICE_ID = "service_qqqt1ng";
const TEMPLATE_ID = "template_r0mo6bd";
const PUBLIC_KEY = '9XlLSnPfvBUUZHMvi';

function Contact() {
  const t = useTranslations();
  const [formData, setFormData] = useState({
    name: '',
    company: '',
    phone: '',
    email: '',
    subject: '',
    message: ''
  });
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prevState => ({
      ...prevState,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Basic validation
    if (!formData.name || !formData.phone || !formData.email || !formData.subject) {
      setSnackbar({
        open: true,
        message: 'Please fill in all required fields',
        severity: 'error'
      });
      return;
    }

    try {
      const result = await emailjs.send(SERVICE_ID, TEMPLATE_ID, {
        name: formData.name,
        company: formData.company,
        phone: formData.phone,
        email: formData.email,
        subject: formData.subject,
        message: formData.message
      }, PUBLIC_KEY);
      
      console.log('Email sent successfully:', result.text);
      setSnackbar({
        open: true,
        message: 'Your message has been prepared to send!',
        severity: 'success'
      });
    } catch (error) {
      console.error('Failed to send email:', error.text);
      setSnackbar({
        open: true,
        message: 'Failed to send message. Please try again later.',
        severity: 'error'
      });
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({
      ...prev,
      open: false
    }));
  };

  return (
    <Stack direction="row" sx={{ justifyContent: "center", minHeight: "65vh", backgroundColor: "black", color: "white" }}>
      <Fade in={true} timeout={2000}>
        <Stack direction="column" height="100%" width={{ lg: "70%", md: "95%", xs: "95%" }}>
          {/* Banner Section */}
          <Box 
            sx={{ 
              height: { xs: "100vh", md: "95vh" },
              width: "100%",
              backgroundImage: `url('/aboutusbanner2.jpg')`,
              backgroundSize: "cover",
              backgroundPosition: "center",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              mb: 6
            }}
          >
            <Typography 
              variant="h1" 
              component="h1" 
              sx={{ 
                color: "white", 
                textAlign: "center",
                fontSize: { xs: "2.5rem", md: "4rem" },
                textShadow: "2px 2px 4px rgba(0,0,0,0.5)"
              }}
            >
             {t('ContactUs.SubjectLine1')}
            </Typography>
          </Box>

          {/* Contact Information */}
          <Box sx={{ mb: 6, px: 3 }}>
            <Typography variant="h2" fontSize={{ lg: "36px", md: "36px", xs: "24px" }} sx={{ mb: 4 }}>
              {t('ContactUs.Subject2Line1')}
            </Typography>
            
            <Grid container spacing={4} sx={{ mb: 6 }}>
              <Grid item xs={12} md={4}>
                <Stack direction="row" spacing={2} alignItems="center">
                  <LocalPhoneOutlinedIcon sx={{ color: "primary.red", fontSize: "2rem" }} />
                  <Box>
                    <Typography variant="h6" sx={{ color: "white" }}>{t('ContactUs.Phone')}</Typography>
                    <Typography variant="body1" sx={{ color: "#ccc" }}>+****************</Typography>
                  </Box>
                </Stack>
              </Grid>
              <Grid item xs={12} md={4}>
                <Stack direction="row" spacing={2} alignItems="center">
                  <EmailOutlinedIcon sx={{ color: "primary.red", fontSize: "2rem" }} />
                  <Box>
                    <Typography variant="h6" sx={{ color: "white" }}>{t('ContactUs.Email')}</Typography>
                    <Typography variant="body1" sx={{ color: "#ccc" }}><EMAIL></Typography>
                  </Box>
                </Stack>
              </Grid>
              <Grid item xs={12} md={4}>
                <Stack direction="row" spacing={2} alignItems="center">
                  <LocationOnOutlinedIcon sx={{ color: "primary.red", fontSize: "2rem" }} />
                  <Box>
                    <Typography variant="h6" sx={{ color: "white" }}>{t('ContactUs.Location')}</Typography>
                    <Typography variant="body1" sx={{ color: "#ccc" }}>Toronto, Canada</Typography>
                  </Box>
                </Stack>
              </Grid>
            </Grid>
          </Box>

          {/* Feedback Form Section */}
          <Box sx={{ mt: 1, mb: 8, px: 3 }}>
            <Typography variant="h2" fontSize={{ lg: "36px", md: "36px", xs: "24px" }} sx={{ textDecoration: "underline", textUnderlineOffset: "1.25vh", mb: 4 }}>
             {t('ContactUs.Subject3Line1')}
            </Typography>
            
            <form onSubmit={handleSubmit}>
              <Grid container spacing={2}>
                {/* First row: Name, Company, Phone */}
                <Grid item xs={12} md={4}> 
                  <TextField 
                    fullWidth
                    name="name"
                    label={t('ContactUs.name')}
                    value={formData.name}
                    onChange={handleChange}
                    required
                    sx={{ mb: 2, bgcolor: "#222", input: { color: "white" }, "& .MuiInputLabel-root": { color: "gray" }, "& .MuiOutlinedInput-root": { color: "white" } }}
                    InputLabelProps={{
                      style: { color: '#aaa' },
                    }}
                  />
                </Grid>
                <Grid item xs={12} md={4}>
                  <TextField 
                    fullWidth
                    name="company"
                    label={t('ContactUs.Company')}
                    value={formData.company}
                    onChange={handleChange}
                    sx={{ mb: 2, bgcolor: "#222", input: { color: "white" }, "& .MuiInputLabel-root": { color: "gray" }, "& .MuiOutlinedInput-root": { color: "white" } }}
                    InputLabelProps={{
                      style: { color: '#aaa' },
                    }}
                  />
                </Grid>
                <Grid item xs={12} md={4}>
                  <TextField 
                    fullWidth
                    name="phone"
                    label={t('ContactUs.Phone')}
                    value={formData.phone}
                    onChange={handleChange}
                    required
                    sx={{ mb: 2, bgcolor: "#222", input: { color: "white" }, "& .MuiInputLabel-root": { color: "gray" }, "& .MuiOutlinedInput-root": { color: "white" } }}
                    InputLabelProps={{
                      style: { color: '#aaa' },
                    }}
                  />
                </Grid>
                
                {/* Second row: Email, Subject */}
                <Grid item xs={12} md={6}>
                  <TextField 
                    fullWidth
                    name="email"
                    label={t('ContactUs.Email')}
                    type="email"
                    value={formData.email}
                    onChange={handleChange}
                    required
                    sx={{ mb: 2, bgcolor: "#222", input: { color: "white" }, "& .MuiInputLabel-root": { color: "gray" }, "& .MuiOutlinedInput-root": { color: "white" } }}
                    InputLabelProps={{
                      style: { color: '#aaa' },
                    }}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField 
                    fullWidth
                    name="subject"
                    label={t('ContactUs.Subject')}
                    value={formData.subject}
                    onChange={handleChange}
                    required
                    sx={{ mb: 2, bgcolor: "#222", input: { color: "white" }, "& .MuiInputLabel-root": { color: "gray" }, "& .MuiOutlinedInput-root": { color: "white" } }}
                    InputLabelProps={{
                      style: { color: '#aaa' },
                    }}
                  />
                </Grid>
                
                {/* Third row: Message */}
                <Grid item xs={12}>
                  <TextField 
                    fullWidth
                    name="message"
                    label={t('ContactUs.Message')}
                    multiline
                    rows={6}
                    value={formData.message}
                    onChange={handleChange}
                    sx={{ mb: 4, bgcolor: "#222", input: { color: "white" }, "& .MuiInputLabel-root": { color: "gray" }, "& .MuiOutlinedInput-root": { color: "white" } }}
                    InputLabelProps={{
                      style: { color: '#aaa' },
                    }}
                  />
                </Grid>
                
                {/* Submit Button */}
                <Grid item xs={12}>
                  <Button 
                    type="submit"
                    variant="contained" 
                    endIcon={<SendIcon />}
                    sx={{ 
                      bgcolor: "primary.red", 
                      color: "white", 
                      px: 4, 
                      py: 1.5,
                      "&:hover": { bgcolor: "primary.darkred" }
                    }}
                  >
                    {t('ContactUs.Send')}
                  </Button>
                </Grid>
              </Grid>
            </form>
          </Box>
        </Stack>
      </Fade>

      <Snackbar 
        open={snackbar.open} 
        autoHideDuration={6000} 
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Stack>
  );
}

export default Contact; 