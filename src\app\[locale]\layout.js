import {NextIntlClientProvider, hasLocale} from 'next-intl';
import {notFound} from 'next/navigation';
import {routing} from '@/i18n/routing';
import { AppRouterCacheProvider } from '@mui/material-nextjs/v15-appRouter';
import "./globals.css";
import NavBar from '@/components/layout/navbar';
import Footer from '@/components/Footer/Footer';
import ThemeProvider from '@/components/ThemeProvider';

export const metadata = {
  title: "Href Technology",
  description: "Technology solutions for your business",
};

export default async function RootLayout({ children, params }) {
  // Ensure that the incoming `locale` is valid
  const {locale} = await params;
  if (!hasLocale(routing.locales, locale)) {
    notFound();
  }

  return (
    <html lang={locale}>
      <body>
         <NextIntlClientProvider>
            <AppRouterCacheProvider options={{ enableCssLayer: true }}>
              <ThemeProvider>
                <NavBar />
                {children}
                <Footer />
              </ThemeProvider>
            </AppRouterCacheProvider>
         </NextIntlClientProvider>
      </body>
    </html>
  );
}
